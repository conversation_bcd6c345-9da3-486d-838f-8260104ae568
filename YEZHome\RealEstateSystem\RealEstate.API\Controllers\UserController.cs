﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;

        public UserController(IUserService userService, IUserDashboardService dashboardService)
        {
            _userService = userService;
            _dashboardService = dashboardService;
        }

        [HttpPut("role")]
        public async Task<ActionResult<AddUserRoleDto>> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            try
            {
                bool isOk = await _userService.AddUserRoleAsync(addUserRoleDto);
                return Ok(isOk);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            try
            {
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }
                return Ok(user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("dashboard")]
        public async Task<ActionResult<UserDashboardDto>> GetUserDashboard()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var dashboard = await _dashboardService.GetUserDashboardAsync(userId.Value);
                return Ok(dashboard);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("wallet")]
        public async Task<ActionResult<WalletInfoDto>> GetUserWallet()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var walletInfo = await _dashboardService.GetUserWalletInfoAsync(userId.Value);
                return Ok(walletInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("properties/stats")]
        public async Task<ActionResult<PropertyStatsDto>> GetUserPropertyStats()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var propertyStats = await _dashboardService.GetUserPropertyStatsAsync(userId.Value);
                return Ok(propertyStats);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("transactions")]
        public async Task<ActionResult<List<WalletTransactionDto>>> GetUserTransactions([FromQuery] int count = 10)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var transactions = await _dashboardService.GetUserTransactionsAsync(userId.Value, count);
                return Ok(transactions);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("ranking")]
        public async Task<ActionResult<MemberRankingDto>> GetUserRanking()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var rankingInfo = await _dashboardService.GetUserMemberRankingInfoAsync(userId.Value);
                return Ok(rankingInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("spending/monthly")]
        public async Task<ActionResult> GetMonthlySpending([FromQuery] int year)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var spendingData = await _dashboardService.GetMonthlySpendingAsync(userId.Value, year);
                return Ok(spendingData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("properties/performance")]
        public async Task<ActionResult> GetPropertyPerformance()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var performanceData = await _dashboardService.GetPropertyPerformanceAsync(userId.Value);
                return Ok(performanceData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPost("deactivate")]
        public async Task<ActionResult> DeactivateAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState); // Returns a 400 Bad Request with validation errors
            }

            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var result = await _userService.DeactivateUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    return BadRequest("Failed to deactivate account. Please check your password and try again.");
                }

                return Ok(new { Message = "Your account has been deactivated successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }


        [HttpDelete("permanent-delete")]
        public async Task<ActionResult> PermanentDeleteAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var result = await _userService.PermanentDeleteUserAsync(userId.Value, deactivateUserDto);
                if (!result)
                {
                    return BadRequest("Failed to delete account. Please check your password and try again.");
                }

                return Ok(new { Message = "Your account and all associated data have been permanently deleted." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpGet("tax-info")]
        public async Task<ActionResult<UserInvoiceInfoDto>> GetUserTaxInfo()
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var user = await _userService.GetUserByIdAsync(userId.Value);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                return Ok(new {
                    PersonalTaxCode = user.PersonalTaxCode,
                    InvoiceInfo = user.InvoiceInfo
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

        [HttpPut("tax-info")]
        public async Task<ActionResult> UpdateUserTaxInfo([FromBody] UpdateUserTaxInfoDto taxInfoDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState); // Returns a 400 Bad Request with validation errors
                }

                var userId = GetUserId();
                if (!userId.HasValue)
                {
                    return Unauthorized("User not authenticated");
                }

                var result = await _userService.UpdateUserTaxInfoAsync(userId.Value, taxInfoDto);
                if (!result)
                {
                    return BadRequest("Failed to update tax information.");
                }

                return Ok(new { Message = "Tax information updated successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }
    }
}
