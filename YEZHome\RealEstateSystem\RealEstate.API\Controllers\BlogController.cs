﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Entities;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;

        public BlogController(IBlogService blogPostService)
        {
            _blogPostService = blogPostService;
        }

        [AllowAnonymous]
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<BlogPostDto>> GetBlogPostBySlug(string slug)
        {
            var blogPost = await _blogPostService.GetBlogBySlugAsync(slug);
            if (blogPost == null)
            {
                return NotFound();
            }
            return Ok(blogPost);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult<IEnumerable<BlogPostDto>>> GetAllBlogPost()
        {
            var result = await _blogPostService.GetAllBlogAsync();
            return Ok(result);
        }

        [HttpGet("blog-posts")]
        [AllowAnonymous]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);
            return Ok(result);
        }
    }
}
