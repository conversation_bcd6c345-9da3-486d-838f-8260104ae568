﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<UserDto> GetUserByIdAsync(Guid id)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            var userDto = _mapper.Map<UserDto>(user);

            // Get user's wallet
            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == id);

            if (wallet == null)
            {
                // Create a new wallet if it doesn't exist
                wallet = new Wallet
                {
                    UserId = id,
                    Balance = 0
                };

                await _unitOfWork.Wallets.AddAsync(wallet);
            }

            // Get user's notification preferences
            var preferences = (await _unitOfWork.NotificationPreferences.FindAsync(p => p.UserId == id)).FirstOrDefault();
            if (preferences == null)
            {
                // Create default notification preferences if they don't exist
                preferences = new NotificationPreference
                {
                    UserId = id,
                    ReceivePromotions = true,
                    ReceiveWalletUpdates = true,
                    ReceiveNews = true,
                    ReceiveCustomerMessages = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.NotificationPreferences.AddAsync(preferences);
            }

            // Save changes if we created a wallet or notification preferences
            if (wallet == null || preferences == null)
            {
                await _unitOfWork.SaveChangesAsync();
            }

            userDto.Wallet = _mapper.Map<WalletDto>(wallet);

            // Map invoice information
            userDto.PersonalTaxCode = user.PersonalTaxCode;
            userDto.InvoiceInfo = new UserInvoiceInfoDto
            {
                BuyerName = user.InvoiceBuyerName,
                Email = user.InvoiceEmail,
                CompanyName = user.InvoiceCompanyName,
                TaxCode = user.InvoiceTaxCode,
                Address = user.InvoiceAddress
            };

            return userDto;
        }

        public async Task<UserDto> GetUserByEmailAsync(string email)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(email);
            return _mapper.Map<UserDto>(user);
        }

        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.AppUsers.GetAllAsync();
            return _mapper.Map<IEnumerable<UserDto>>(users);
        }

        public async Task<UserDto> CreateUserAsync(CreateUserDto userDto)
        {
            var user = _mapper.Map<AppUser>(userDto);
            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Create default wallet for the user
            var wallet = new Wallet
            {
                UserId = user.Id,
                Balance = 0
            };

            await _unitOfWork.Wallets.AddAsync(wallet);

            await _unitOfWork.SaveChangesAsync();
            return _mapper.Map<UserDto>(user);
        }

        public async Task<bool> UpdateUserAsync(Guid id, CreateUserDto userDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return false;
            _mapper.Map(userDto, user);
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteUserAsync(Guid id)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return false;
            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AddUserRoleAsync(AddUserRoleDto addUserRoleDto)
        {
            var userRole = _mapper.Map<UserRole>(addUserRoleDto);
            await _unitOfWork.UserRoles.AddAsync(userRole);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateUserAvatarAsync(Guid userId, string avatarImage)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            user.AvatarImage = avatarImage;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<string> GetUserAvatarImageAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            return user?.AvatarImage;
        }

        public async Task<bool> DeactivateUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Validate password before deactivating
            var authService = new AuthService(_unitOfWork, null);
            var isValidCredentials = await authService.ValidateUserCredentialsAsync(user.Email, deactivateUserDto.Password);
            if (!isValidCredentials) return false;

            // Deactivate the user
            user.IsActive = false;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ReactivateUserAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Reactivate the user
            user.IsActive = true;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> PermanentDeleteUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Validate password before permanent deletion
            var authService = new AuthService(_unitOfWork, null);
            var isValidCredentials = await authService.ValidateUserCredentialsAsync(user.Email, deactivateUserDto.Password);
            if (!isValidCredentials) return false;

            // Delete related data
            // 1. Delete user roles
            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .ToListAsync();
            foreach (var role in userRoles)
            {
                _unitOfWork.UserRoles.Remove(role);
            }

            // 2. Delete wallet and transactions
            var wallet = await _unitOfWork.Wallets.GetQueryable()
                .FirstOrDefaultAsync(w => w.UserId == userId);
            if (wallet != null)
            {
                var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                    .Where(t => t.UserId == userId)
                    .ToListAsync();
                foreach (var transaction in transactions)
                {
                    _unitOfWork.WalletTransactions.Remove(transaction);
                }
                _unitOfWork.Wallets.Remove(wallet);
            }

            // 3. Delete user favorites
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => f.UserID == userId)
                .ToListAsync();
            foreach (var favorite in favorites)
            {
                _unitOfWork.UserFavorites.Remove(favorite);
            }


            // 8. Delete properties
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId)
                .ToListAsync();
            foreach (var property in properties)
            {
                // Delete property media
                var propertyMedia = await _unitOfWork.PropertyMedias.GetQueryable()
                    .Where(pm => pm.PropertyID == property.Id)
                    .ToListAsync();
                foreach (var media in propertyMedia)
                {
                    _unitOfWork.PropertyMedias.Remove(media);
                }

                // Delete property status logs
                var statusLogs = await _unitOfWork.PropertyStatusLogs.GetQueryable()
                    .Where(sl => sl.PropertyID == property.Id)
                    .ToListAsync();
                foreach (var log in statusLogs)
                {
                    _unitOfWork.PropertyStatusLogs.Remove(log);
                }

                _unitOfWork.Properties.Remove(property);
            }

            // Finally, delete the user
            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateUserTaxInfoAsync(Guid userId, UpdateUserTaxInfoDto taxInfoDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return false;

            // Update personal tax code
            user.PersonalTaxCode = taxInfoDto.PersonalTaxCode;

            // Update invoice information if provided
            if (taxInfoDto.InvoiceInfo != null)
            {
                user.InvoiceBuyerName = taxInfoDto.InvoiceInfo.BuyerName;
                user.InvoiceEmail = taxInfoDto.InvoiceInfo.Email;
                user.InvoiceCompanyName = taxInfoDto.InvoiceInfo.CompanyName;
                user.InvoiceTaxCode = taxInfoDto.InvoiceInfo.TaxCode;
                user.InvoiceAddress = taxInfoDto.InvoiceInfo.Address;
            }

            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<UserInvoiceInfoDto> GetUserInvoiceInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return null;

            return new UserInvoiceInfoDto
            {
                BuyerName = user.InvoiceBuyerName,
                Email = user.InvoiceEmail,
                CompanyName = user.InvoiceCompanyName,
                TaxCode = user.InvoiceTaxCode,
                Address = user.InvoiceAddress
            };
        }

        public async Task<bool> IsUserExistsAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            return user != null;
        }
    }
}
