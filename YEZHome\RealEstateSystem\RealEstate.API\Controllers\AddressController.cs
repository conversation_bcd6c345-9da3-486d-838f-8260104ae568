﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Infrastructure;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public AddressController(ApplicationDbContext context)
        {
            _context = context;
        }

        [HttpGet("cities")]
        public async Task<ActionResult<IEnumerable<City>>> GetCitiesAsync()
        {
            return await _context.City.OrderBy(x=>x.Id).ToListAsync();
        }

        [HttpGet("cities/{cityId}/districts")]
        public async Task<ActionResult<IEnumerable<District>>> GetDistrictByCityAsync(int cityId)
        {
            var districts = await _context.District.Where(d => d.CityId == cityId).OrderBy(x=>x.NameWithType).ToListAsync();
            if (districts == null || !districts.Any())
            {
                return NotFound();
            }
            return districts;
        }

        [HttpGet("districts/{districtId}/wards")]
        public async Task<ActionResult<IEnumerable<Ward>>> GetWardByDistrictAsync(int districtId)
        {
            var wards = await _context.Ward.Where(d => d.DistrictId == districtId).OrderBy(x => x.NameWithType).ToListAsync();
            if (wards == null || !wards.Any())
            {
                return NotFound();
            }
            return wards;
        }

        [HttpGet("districts/{districtId}/streets")]
        public async Task<ActionResult<IEnumerable<Street>>> GetStreetByDistrictAsync(int districtId)
        {
            var streets = await _context.Street.Where(s => s.DistrictId == districtId).ToListAsync();
            if (streets == null || !streets.Any())
            {
                return NotFound();
            }
            return streets;
        }

        [HttpGet("wards/{wardId}/streets/{streetId}/projects")]
        public async Task<ActionResult<IEnumerable<Project>>> GetProjectsByWardStreetAsync(int wardId, int streetId)
        {
            var projects = await _context.Project.Where(p => p.WardId == wardId && p.StreetId == streetId).ToListAsync();
            if (projects == null || !projects.Any())
            {
                return NotFound();
            }
            return projects;
        }
    }
}
